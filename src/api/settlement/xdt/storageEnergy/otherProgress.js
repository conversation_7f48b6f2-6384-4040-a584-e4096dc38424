import request from "@/utils/request";

/**
 * 新电途储能管理 - 他投结算进度API接口
 * 提供他投结算进度信息的增删改查功能
 */
export default {
  /**
   * 分页查询他投结算进度信息
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {Array<number>} [data.orgNoList] - 机构编号列表
   * @param {number} [data.operatorId] - 操作员ID
   * @param {string} [data.operatorName] - 操作员名称
   * @param {string} [data.operator] - 运营商（模糊查询）
   * @param {string} [data.investorName] - 投资方名称（模糊查询）
   * @param {string} [data.siteName] - 场站名称（模糊查询）
   * @param {string} [data.paymentProgress] - 付款进度
   * @param {string} [data.reconciliationPerson] - 对账负责人（模糊查询）
   * @param {string} [data.billYearMonthStart] - 账单年月开始
   * @param {string} [data.billYearMonthEnd] - 账单年月结束
   * @returns {Promise<Object>} 返回分页查询结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Array<Object>} returns.data - 他投结算进度信息列表
   * @returns {number} returns.data[].id - 记录ID
   * @returns {string} returns.data[].billYearMonth - 账单年月
   * @returns {string} returns.data[].investorName - 投资方名称
   * @returns {string} returns.data[].operator - 运营商
   * @returns {string} returns.data[].siteName - 场站名称
   * @returns {string} returns.data[].stationRatio - 场站分成比例
   * @returns {string} returns.data[].investorRatio - 投资方分成比例
   * @returns {string} returns.data[].platformRatio - 平台分成比例
   * @returns {number} returns.data[].chargingAmount - 充电量(kWh)
   * @returns {number} returns.data[].chargingFee - 充电费用(元)
   * @returns {number} returns.data[].dischargingAmount - 放电量(kWh)
   * @returns {number} returns.data[].dischargingFee - 放电费用(元)
   * @returns {string} returns.data[].electricityRate - 电价
   * @returns {number} returns.data[].energyManagementIncome - 能源管理收入(元)
   * @returns {number} returns.data[].energyManagementFee - 能源管理费用(元)
   * @returns {number} returns.data[].assetIncome - 资产收入(元)
   * @returns {number} returns.data[].platformIncome - 平台收入(元)
   * @returns {string} returns.data[].paymentProgress - 付款进度
   * @returns {string} returns.data[].reconciliationPerson - 对账负责人
   * @returns {string} returns.data[].remarks - 备注
   * @returns {string} returns.data[].createBy - 创建人
   * @returns {string} returns.data[].createTime - 创建时间
   * @returns {string} returns.data[].updateBy - 更新人
   * @returns {string} returns.data[].updateTime - 更新时间
   * @returns {number} returns.pageNum - 当前页码
   * @returns {number} returns.pageSize - 每页条数
   * @returns {number} returns.total - 总记录数
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 查询第一页数据
   * const result = await otherProgressApi.list({
   *   pageNum: 1,
   *   pageSize: 10,
   *   operator: '运营商名称'
   * });
   */
  list(data) {
    return request({
      url: "/st/newcharge/third/queryPage",
      method: "post",
      data: data,
    });
  },

  //新增
  add(data) {
    return request({
      url: "/st/newcharge/third/add",
      method: "post",
      data: data,
    });
  },

  /**
   * 编辑他投结算进度信息
   * @param {Object} data - 他投结算进度信息数据
   * @param {number} data.id - 他投结算进度信息ID（必填）
   * @param {string} [data.billYearMonth] - 账单年月
   * @param {string} [data.investorName] - 投资方名称
   * @param {string} [data.operator] - 运营商
   * @param {string} [data.siteName] - 场站名称
   * @param {string} [data.stationRatio] - 场站分成比例
   * @param {string} [data.investorRatio] - 投资方分成比例
   * @param {string} [data.platformRatio] - 平台分成比例
   * @param {number} [data.chargingAmount] - 充电量(kWh)
   * @param {number} [data.chargingFee] - 充电费用(元)
   * @param {number} [data.dischargingAmount] - 放电量(kWh)
   * @param {number} [data.dischargingFee] - 放电费用(元)
   * @param {string} [data.electricityRate] - 电价
   * @param {number} [data.energyManagementIncome] - 能源管理收入(元)
   * @param {number} [data.energyManagementFee] - 能源管理费用(元)
   * @param {number} [data.assetIncome] - 资产收入(元)
   * @param {number} [data.platformIncome] - 平台收入(元)
   * @param {string} [data.paymentProgress] - 付款进度
   * @param {string} [data.reconciliationPerson] - 对账负责人
   * @param {string} [data.remarks] - 备注
   * @returns {Promise<Object>} 返回编辑结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 操作结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 编辑他投结算进度信息
   * const result = await otherProgressApi.update({
   *   id: 1,
   *   paymentProgress: '已付款'
   * });
   */
  update(data) {
    return request({
      url: "/st/newcharge/third/edit",
      method: "post",
      data: data,
    });
  },

  /**
   * 删除他投结算进度信息
   * @param {Object} data - 删除参数
   * @param {number|Array<number>} data.id - 要删除的他投结算进度信息ID，支持单个ID或ID数组
   * @returns {Promise<Object>} 返回删除结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 删除结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 删除单个他投结算进度信息
   * const result = await otherProgressApi.delete({ id: 1 });
   *
   * // 批量删除他投结算进度信息
   * const result = await otherProgressApi.delete({ id: [1, 2, 3] });
   */
  delete(data) {
    return request({
      url: "/st/newcharge/third/delete",
      method: "get",
      params: data,
    });
  },

  /**
   * 导出他投结算进度信息Excel文件
   * @param {Object} data - 导出参数（与查询参数相同，用于筛选导出数据）
   * @param {string} [data.operator] - 运营商筛选条件
   * @param {string} [data.investorName] - 投资方名称筛选条件
   * @param {string} [data.siteName] - 场站名称筛选条件
   * @param {string} [data.paymentProgress] - 付款进度筛选条件
   * @param {string} [data.reconciliationPerson] - 对账负责人筛选条件
   * @param {string} [data.billYearMonthStart] - 账单年月开始筛选条件
   * @param {string} [data.billYearMonthEnd] - 账单年月结束筛选条件
   * @returns {Promise<Object>} 返回导出结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 导出文件下载链接或文件内容
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导出所有他投结算进度信息
   * const result = await otherProgressApi.export({
   *   operator: '运营商名称'
   * });
   */
  export(data) {
    return request({
      url: "/st/newcharge/third/exportExcel",
      method: "post",
      data: data,
    });
  },

  /**
   * 导入他投结算进度信息Excel文件
   * @param {FormData} data - 包含文件的FormData对象
   * @param {File} data.file - 要导入的Excel文件（必填）
   * @returns {Promise<Object>} 返回导入结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息（包含导入成功/失败信息）
   * @returns {string} returns.data - 导入结果详情
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导入Excel文件
   * const formData = new FormData();
   * formData.append('file', file);
   * const result = await otherProgressApi.import(formData);
   */
  import(data) {
    return request({
      url: "/st/newcharge/third/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      skipIntercept: true,
    });
  },

  /**
   * 获取他投结算进度相关的下拉列表数据
   * 用于表单选择器的数据源
   * @returns {Promise<Object>} 返回下拉列表数据
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Object} returns.data - 下拉列表数据对象，key为字段名，value为选项数组
   * @returns {Array<string>} [returns.data.operators] - 运营商选项列表
   * @returns {Array<string>} [returns.data.investorNames] - 投资方名称选项列表
   * @returns {Array<string>} [returns.data.reconciliationPersons] - 对账负责人选项列表
   * @returns {Array<string>} [returns.data.paymentProgresses] - 付款进度选项列表
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 获取下拉列表数据
   * const result = await otherProgressApi.getDropLists();
   * // result.data = {
   * //   operators: ['运营商A', '运营商B'],
   * //   investorNames: ['投资方A', '投资方B'],
   * //   reconciliationPersons: ['张三', '李四'],
   * //   paymentProgresses: ['未付款', '部分付款', '已付款']
   * // }
   */
  getDropLists() {
    return request({
      url: "/st/newcharge/third/getDropLists",
      method: "get",
    });
  },

  // 批量操作
  batchOperation(data) {
    return request({
      url: "/st/newcharge/third/batchOperation",
      method: "post",
      data: data,
    });
  },
};
