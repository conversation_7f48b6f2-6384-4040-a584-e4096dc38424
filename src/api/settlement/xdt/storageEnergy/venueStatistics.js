import request from "@/utils/request";

/**
 * 新电途储能管理 - 场地回款统计API接口
 * 提供场地回款统计信息的增删改查功能
 */
export default {
  /**
   * 分页查询场地回款统计信息
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {Array<number>} [data.orgNoList] - 机构编号列表
   * @param {number} [data.operatorId] - 操作员ID
   * @param {string} [data.operatorName] - 操作员名称
   * @param {string} [data.operatorName] - 运营商名称（模糊查询）
   * @param {string} [data.fundPath] - 资金路径
   * @returns {Promise<Object>} 返回分页查询结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Array<Object>} returns.data - 场地回款统计信息列表
   * @returns {number} returns.data[].id - 记录ID
   * @returns {string} returns.data[].operatorName - 运营商名称
   * @returns {string} returns.data[].fundPath - 资金路径
   * @returns {number} returns.data[].totalReceivable - 应收总额(元)
   * @returns {number} returns.data[].custodyRecoveryAmount - 托管回收金额(元)
   * @returns {number} returns.data[].offlinePaymentAmount - 线下付款金额(元)
   * @returns {number} returns.data[].electricityPurchaseDeduction - 购电扣款(元)
   * @returns {number} returns.data[].totalPaymentAmount - 已付款总额(元)
   * @returns {number} returns.data[].pendingPaymentAmount - 待付款金额(元)
   * @returns {string} returns.data[].remarks - 备注
   * @returns {string} returns.data[].createBy - 创建人
   * @returns {string} returns.data[].createTime - 创建时间
   * @returns {string} returns.data[].updateBy - 更新人
   * @returns {string} returns.data[].updateTime - 更新时间
   * @returns {number} returns.pageNum - 当前页码
   * @returns {number} returns.pageSize - 每页条数
   * @returns {number} returns.total - 总记录数
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 查询第一页数据
   * const result = await venueStatisticsApi.list({
   *   pageNum: 1,
   *   pageSize: 10,
   *   operatorName: '运营商名称'
   * });
   */
  list(data) {
    return request({
      url: "/st/newcharge/station/payment/queryPage",
      method: "post",
      data: data,
    });
  },

  // 新增
  add(data) {
    return request({
      url: "/st/newcharge/station/payment/add",
      method: "post",
      data: data,
    });
  },

  // 编辑
  update(data) {
    return request({
      url: "/st/newcharge/station/payment/edit",
      method: "post",
      data: data,
    });
  },

  // 删除
  delete(data) {
    return request({
      url: "/st/newcharge/station/payment/delete",
      method: "get",
      params: data,
    });
  },

  // 导出
  export(data) {
    return request({
      url: "/st/newcharge/station/payment/exportExcel",
      method: "post",
      data: data,
    });
  },

  // 导入
  import(data) {
    return request({
      url: "/st/newcharge/station/payment/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      skipIntercept: true,
    });
  },

  // 获取下拉列表数据
  getDropLists() {
    return request({
      url: "/st/newcharge/station/payment/getDropLists",
      method: "get",
    });
  },

  // 批量操作
  batchOperation(data) {
    return request({
      url: "/st/newcharge/station/payment/batchOperation",
      method: "post",
      data: data,
    });
  },
};
