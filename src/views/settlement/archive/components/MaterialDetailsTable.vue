<!-- 材料明细表格组件 -->
<template>
  <div class="material-details-table">
    <div class="table-header">
      <div class="header-actions">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="small"
          @click="addRow"
        >
          新增
        </el-button>
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="small"
          :disabled="selectedRows.length === 0"
          @click="deleteSelected"
        >
          删除
        </el-button>
        <el-upload
          ref="batchUpload"
          :action="uploadAction"
          :headers="uploadHeaders"
          :multiple="true"
          :show-file-list="false"
          :before-upload="beforeBatchUpload"
          :on-success="onBatchUploadSuccess"
          :on-error="onBatchUploadError"
          accept=".doc,.docx,.xls,.xlsx,.pdf,.jpg,.jpeg,.png"
          style="display: inline-block; margin-left: 10px;"
        >
          <el-button type="success" icon="el-icon-upload" size="small">
            批量上传文件
          </el-button>
        </el-upload>
      </div>
    </div>

    <div class="table-container">
      <el-table
        :data="tableData"
        border
        stripe
        max-height="400"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />

        <el-table-column label="全部" width="80" align="center">
          <template slot-scope="scope">
            <el-checkbox
              v-model="scope.row.selectAll"
              @change="handleSelectAll"
            />
          </template>
        </el-table-column>

        <el-table-column label="类型" width="120" align="center">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.type"
              placeholder="请选择类型"
              size="small"
              style="width: 100%"
            >
              <el-option
                v-for="item in fileTypeOptions"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="文件名称" min-width="200" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.fileUrl">
              <el-link
                type="primary"
                @click="previewFile(scope.row)"
                :underline="false"
              >
                {{ scope.row.fileName }}
              </el-link>
            </div>
            <span v-else>{{ scope.row.fileName || "——" }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <el-upload
              :action="uploadAction"
              :headers="uploadHeaders"
              :show-file-list="false"
              :before-upload="beforeUpload"
              :on-success="
                (response, file) =>
                  onUploadSuccess(response, file, scope.$index)
              "
              :on-error="onUploadError"
              accept=".doc,.docx,.xls,.xlsx,.pdf,.jpg,.jpeg,.png"
              style="display: inline-block; margin-right: 5px;"
            >
              <el-button type="primary" size="mini" icon="el-icon-upload">
                上传
              </el-button>
            </el-upload>

            <el-button
              type="danger"
              size="mini"
              icon="el-icon-delete"
              @click="deleteRow(scope.$index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";

export default {
  name: "MaterialDetailsTable",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    fileTypeOptions: {
      type: Array,
      default: () => [
        { dictLabel: "收入", dictValue: "income" },
        { dictLabel: "支出", dictValue: "expense" },
      ],
    },
  },
  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      selectedRows: [],
      uploadAction: baseUrl + "/doc/upload", // 上传接口地址
      uploadHeaders: {
        // 上传请求头
        Authorization: "Bearer " + getToken(),
      },
    };
  },
  computed: {
    tableData: {
      get() {
        return this.value || [];
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  mounted() {
    // 如果没有数据，初始化一行
    if (!this.tableData.length) {
      this.addRow();
    }
  },
  methods: {
    // 新增行
    addRow() {
      const newRow = {
        type: "",
        fileName: "",
        fileUrl: "",
        fileSize: 0,
        selectAll: false,
      };
      this.tableData.push(newRow);
      this.$emit("input", this.tableData);
    },

    // 删除行
    deleteRow(index) {
      this.$confirm("确定要删除这一行吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.tableData.splice(index, 1);
          this.$emit("input", this.tableData);
          this.$message.success("删除成功");
        })
        .catch(() => {
          // 用户取消删除
        });
    },

    // 删除选中行
    deleteSelected() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请选择要删除的行");
        return;
      }

      this.$confirm(
        `确定要删除选中的${this.selectedRows.length}行吗？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          // 根据选中的行删除数据
          const selectedIndexes = this.selectedRows
            .map((row) => this.tableData.findIndex((item) => item === row))
            .sort((a, b) => b - a); // 从后往前删除，避免索引变化

          selectedIndexes.forEach((index) => {
            if (index > -1) {
              this.tableData.splice(index, 1);
            }
          });

          this.$emit("input", this.tableData);
          this.$message.success("删除成功");
          this.selectedRows = [];
        })
        .catch(() => {
          // 用户取消删除
        });
    },

    // 处理表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    // 处理全选
    handleSelectAll() {
      // 这里可以实现全选逻辑，暂时只是一个复选框
    },

    // 单文件上传前的处理
    beforeUpload(file) {
      const isValidType = this.checkFileType(file);
      const isValidSize = this.checkFileSize(file);

      if (!isValidType) {
        this.$message.error(
          "文件格式不正确，请上传 Word、Excel、PDF、JPG、PNG 格式的文件"
        );
        return false;
      }

      if (!isValidSize) {
        return false;
      }

      return true;
    },

    // 单文件上传成功
    onUploadSuccess(response, file, index) {
      if (response && response.code == 10000) {
        // 根据系统标准，response.data 是一个数组
        if (response.data && response.data.length > 0) {
          const fileData = response.data[0];
          this.tableData[index].fileName = fileData.docName || file.name;
          this.tableData[index].fileUrl = fileData.storePath;
          this.tableData[index].fileSize = file.size;
          this.$emit("input", this.tableData);
          this.$message.success("上传成功");
        }
      } else {
        this.$message.error(response?.msg || "上传失败");
      }
    },

    // 单文件上传失败
    onUploadError(error) {
      console.error("上传失败:", error);
      this.$message.error("上传失败，请重试");
    },

    // 批量上传前的处理
    beforeBatchUpload(file) {
      return this.checkFileType(file) && this.checkFileSize(file);
    },

    // 批量上传成功
    onBatchUploadSuccess(response, file) {
      if (response && response.code == 10000) {
        // 根据系统标准，response.data 是一个数组
        if (response.data && response.data.length > 0) {
          const fileData = response.data[0];

          // 检查是否有重名文件
          const existingIndex = this.tableData.findIndex(
            (item) => item.fileName === file.name && item.fileSize === file.size
          );

          if (existingIndex > -1) {
            // 覆盖现有文件
            this.tableData[existingIndex].fileName =
              fileData.docName || file.name;
            this.tableData[existingIndex].fileUrl = fileData.storePath;
            this.tableData[existingIndex].fileSize = file.size;
          } else {
            // 添加新行
            this.tableData.push({
              type: "",
              fileName: fileData.docName || file.name,
              fileUrl: fileData.storePath,
              fileSize: file.size,
              selectAll: false,
            });
          }

          this.$emit("input", this.tableData);
          this.$message.success(`${file.name} 上传成功`);
        }
      } else {
        this.$message.error(
          `${file.name} 上传失败: ${response?.msg || "未知错误"}`
        );
      }
    },

    // 批量上传失败
    onBatchUploadError(error, file) {
      console.error("批量上传失败:", error);
      this.$message.error(`${file.name} 上传失败`);
    },

    // 检查文件类型
    checkFileType(file) {
      const allowedTypes = [
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/pdf",
        "image/jpeg",
        "image/jpg",
        "image/png",
      ];

      const fileExtension = file.name
        .split(".")
        .pop()
        .toLowerCase();
      const allowedExtensions = [
        "doc",
        "docx",
        "xls",
        "xlsx",
        "pdf",
        "jpg",
        "jpeg",
        "png",
      ];

      return (
        allowedTypes.includes(file.type) ||
        allowedExtensions.includes(fileExtension)
      );
    },

    // 检查文件大小
    checkFileSize(file) {
      const maxSize = 20; // 20MB
      const isLt2M = file.size / 1024 / 1024 < maxSize;
      if (!isLt2M) {
        this.$message.error(`上传的文件大小不能超过${maxSize}MB!`);
      }
      return isLt2M;
    },

    // 预览文件
    previewFile(row) {
      if (row.fileUrl) {
        // 这里可以调用文件预览组件或直接打开文件
        window.open(row.fileUrl, "_blank");
      } else {
        this.$message.warning("暂无文件可预览");
      }
    },
  },
};
</script>

<style scoped>
.material-details-table {
  width: 100%;
}

.table-header {
  margin-bottom: 10px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.table-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.el-table {
  border-radius: 4px;
}

.el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

.el-table td {
  padding: 8px 0;
}

.el-select,
.el-input {
  width: 100%;
}

.el-button + .el-button {
  margin-left: 5px;
}
</style>
