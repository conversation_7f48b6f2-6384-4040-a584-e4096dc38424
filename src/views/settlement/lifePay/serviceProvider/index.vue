<!-- 服务商信息 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['serviceProvider:list:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['serviceProvider:list:import']"
          >导入</el-button
        >
        <el-button
          type="primary"
          @click="handleAdd"
          v-has-permi="['serviceProvider:list:add']"
          >新增</el-button
        >
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入服务商信息"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/lifePay/serviceProvider/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import moment from "moment";
import BatchUpload from "@/components/BatchUpload/index.vue";
import { queryLog } from "@/api/common.js";
import Timeline from "@/components/Timeline/index.vue";

export default {
  name: "lifePayServiceProvider",
  components: { BatchUpload, Timeline },
  mixins: [exportMixin],
  data() {
    return {
      recordList: [],
      uploadObj: {
        api: "/st/lifePay/providerInfo/importExcel",
        url: "/charging-maintenance-ui/static/服务商信息导入模板.xlsx",
        extraData: {},
      },
      workLoading: false,
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        recordId: "",
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "editStatus",
      //buse参数-e

      statusOptions: [],
      agreementTypeOptions: [],
      discountOptions: [],
      billingInstitutionOptions: [],
      pidOptions: [],
      midOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取状态字典
    this.getDicts("st_provider_status").then((response) => {
      this.statusOptions = response.data;
    });

    // 获取协议类型字典
    this.getDicts("st_agreement_type").then((response) => {
      this.agreementTypeOptions = response.data;
    });

    // 获取是否打折字典
    this.getDicts("sys_yes_no").then((response) => {
      this.discountOptions = response.data;
    });

    // 获取下拉列表数据
    api.getDropLists().then((res) => {
      if (res.success) {
        // 处理下拉列表数据
        if (res.data.billingInstitution) {
          this.billingInstitutionOptions = res.data.billingInstitution.map(
            (item) => ({
              dictLabel: item,
              dictValue: item,
            })
          );
        }
        if (res.data.pid) {
          this.pidOptions = res.data.pid.map((item) => ({
            dictLabel: item,
            dictValue: item,
          }));
        }
        if (res.data.mid) {
          this.midOptions = res.data.mid.map((item) => ({
            dictLabel: item,
            dictValue: item,
          }));
        }
      }
    });

    // this.loadData();
  },
  activated() {
    this.loadData();
  },
  methods: {
    checkPermission,

    handleBatchAdd() {
      this.$refs.batchUpload.open();
    },
    handleAdd() {
      this.$refs.crud.switchModalView(true, "ADD");
    },
    rowEdit(row) {
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
        commissionTimeRange: [row.commissionStartTime, row.commissionEndTime],
      });
    },
    handleDownload(row) {},
    handlePreview(row) {},
    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "commissionTimeRange",
          title: "返佣起止时间",
          startFieldName: "commissionStartTime",
          endFieldName: "commissionEndTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0];
          params[x.endFieldName] = params[x.field][1];
          delete params[x.field];
        }
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType:add/update
      this.handleTimeRange(params);
      const res = await api[crudOperationType](params);
      if (res?.code === "10000") {
        this.$message.success("提交成功");
        this.loadData();
      } else {
        return false;
      }
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          id: row.id,
        };
        api.delete(params).then((res) => {
          if (res?.code === "10000") {
            this.$message.success("删除成功");
            this.loadData();
          }
        });
      });
    },
    handleLog(row) {
      queryLog({ businessId: row.id }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "serviceProvider",
          title: "服务商",
          width: 180,
        },
        {
          field: "agreementType",
          title: "协议类型",
          width: 120,
        },
        {
          field: "billingInstitution",
          title: "出账机构",
          width: 120,
        },
        {
          field: "mid",
          title: "MID",
          width: 120,
        },
        {
          field: "pid",
          title: "PID",
          width: 120,
        },
        {
          field: "goLiveTime",
          title: "上线时间",
          width: 120,
        },
        {
          field: "commissionStartTime",
          title: "返佣起始时间",
          width: 120,
        },
        {
          field: "commissionEndTime",
          title: "返佣截止时间",
          width: 120,
        },
        {
          field: "lastCommissionRatio",
          title: "上年协议返佣比例",
          width: 120,
        },
        {
          field: "rate",
          title: "费率",
          width: 120,
        },
        {
          field: "isDiscounted",
          title: "是否打折",
          width: 120,
        },
        {
          field: "currCommissionRatio",
          title: "今年协议返佣比例",
          width: 120,
        },
        {
          field: "remark",
          title: "备注",
          width: 120,
        },
        {
          field: "status",
          title: "状态",
          width: 120,
        },
        {
          field: "archiveTime",
          title: "归档时间",
          width: 120,
        },
        {
          field: "originProvider",
          title: "原服务商",
          width: 120,
        },
        {
          field: "billingInstitutionCode",
          title: "出账机构编码",
          width: 120,
        },
        {
          field: "updateBy",
          title: "操作人",
          width: 120,
        },
        {
          field: "updateTime",
          title: "操作时间",
          width: 120,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "serviceProvider",
            element: "el-input",
            title: "服务商",
          },
          {
            field: "pid",
            title: "PID",
            element: "el-select",
            props: {
              options: this.pidOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "mid",
            title: "MID",
            element: "el-select",
            props: {
              options: this.midOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "commissionTimeRange",
            title: "返佣起止时间",
            element: "el-date-picker",
            props: {
              type: "monthrange",
              valueFormat: "yyyy-MM",
            },
          },
          {
            field: "billingInstitution",
            title: "出账机构",
            element: "el-select",
            props: {
              options: this.billingInstitutionOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["serviceProvider:list:edit"]),
        delBtn: checkPermission(["serviceProvider:list:delete"]),
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "60%",
        formConfig: [
          {
            field: "serviceProvider",
            title: "服务商",
            element: "el-input",
            rules: [
              { required: true, message: "请输入服务商", trigger: "blur" },
            ],
          },
          {
            field: "agreementType",
            title: "协议类型",
            element: "el-select",
            props: {
              options: this.agreementTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
            rules: [
              { required: true, message: "请选择协议类型", trigger: "blur" },
            ],
          },
          {
            field: "billingInstitution",
            title: "出账机构",
            rules: [
              {
                required: true,
                message: "请输入出账机构",
                trigger: "blur",
              },
            ],
          },
          {
            field: "mid",
            title: "mid",
            rules: [{ required: true, message: "请输入mid", trigger: "blur" }],
          },
          {
            field: "pid",
            title: "pid",
            rules: [{ required: true, message: "请输入pid", trigger: "blur" }],
          },
          {
            field: "onlineTimeRange",
            title: "上线时间",
            element: "el-date-picker",
            props: {
              type: "month",
              valueFormat: "yyyy-MM",
            },
          },
          {
            field: "commissionTimeRange",
            title: "返佣起止时间",
            element: "el-date-picker",
            props: {
              type: "monthrange",
              valueFormat: "yyyy-MM",
            },
          },
          {
            field: "lastCommissionRatio",
            title: "上年协议返佣比例",
            element: "el-input",
            slots: {
              append: "%",
            },
          },
          {
            field: "rate",
            title: "费率",
            rules: [{ required: true, message: "请输入费率", trigger: "blur" }],
            element: "el-input",
          },
          {
            field: "isDiscounted",
            title: "是否打折",
            element: "el-select",
            props: {
              options: this.discountOptions,
              optionLabel: "dictLabel",
              optionValue: "dictLabel",
              filterable: true,
            },
          },
          {
            field: "currCommissionRatio",
            title: "今年协议返佣比例",
            element: "el-input",
            slots: {
              append: "%",
            },
            rules: [{ required: true, message: "请输入", trigger: "blur" }],
          },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: this.statusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
            rules: [{ required: true, message: "请选择状态", trigger: "blur" }],
          },
          {
            field: "archiveTime",
            title: "归档时间",
            element: "el-date-picker",
            props: {
              type: "month",
              valueFormat: "yyyy-MM",
            },
          },
          {
            field: "originProvider",
            title: "原服务商",
            element: "el-input",
          },
          {
            field: "billingInstitutionCode",
            title: "出账机构编码",
            element: "el-input",
          },
          {
            field: "remark",
            title: "备注",
            element: "el-input",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "500个字符以内",
            },
          },
        ],

        crudPermission: [],
        customOperationTypes: [
          {
            title: "日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["serviceProvider:list:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "160px",
        },
      };
    },
  },
};
</script>

<style></style>
